-- EU Funds MCP Server Database Schema
-- Supabase PostgreSQL schema with pgvector extension

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Create crawled_pages table for storing crawled content with embeddings
CREATE TABLE IF NOT EXISTS crawled_pages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    url TEXT NOT NULL UNIQUE,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding vector(1536), -- OpenAI text-embedding-3-small dimension
    crawled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    content_hash TEXT, -- For deduplication
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived', 'error'))
);

-- Create funding_programs table for structured funding information
CREATE TABLE IF NOT EXISTS funding_programs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    program_type TEXT, -- horizon-europe, erasmus+, interreg, etc.
    deadline TIMESTAMP WITH TIME ZONE,
    budget_min BIGINT, -- in euros
    budget_max BIGINT, -- in euros
    eligibility JSONB DEFAULT '{}', -- structured eligibility criteria
    call_id TEXT,
    source_url TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    extracted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'closed', 'draft', 'cancelled'))
);

-- Create funding_calls table for specific call instances
CREATE TABLE IF NOT EXISTS funding_calls (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    program_id UUID REFERENCES funding_programs(id) ON DELETE CASCADE,
    call_identifier TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    opening_date TIMESTAMP WITH TIME ZONE,
    deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    budget_available BIGINT, -- in euros
    expected_grants INTEGER, -- number of grants expected
    eligibility_criteria JSONB DEFAULT '{}',
    evaluation_criteria JSONB DEFAULT '{}',
    documents JSONB DEFAULT '[]', -- array of document links
    source_url TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'open' CHECK (status IN ('open', 'closed', 'upcoming', 'cancelled'))
);

-- Create organizations table for tracking organization types and success rates
CREATE TABLE IF NOT EXISTS organizations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    organization_type TEXT NOT NULL, -- NGO, university, company, etc.
    country TEXT,
    sector TEXT,
    size_category TEXT, -- small, medium, large
    success_rate DECIMAL(3,2), -- success rate 0.00-1.00
    total_applications INTEGER DEFAULT 0,
    successful_applications INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create applications table for tracking application history
CREATE TABLE IF NOT EXISTS applications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    call_id UUID REFERENCES funding_calls(id) ON DELETE CASCADE,
    project_title TEXT NOT NULL,
    project_description TEXT,
    requested_amount BIGINT, -- in euros
    application_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    decision_date TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'submitted' CHECK (status IN ('draft', 'submitted', 'under_review', 'approved', 'rejected', 'withdrawn')),
    awarded_amount BIGINT, -- in euros, if approved
    feedback TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create search_queries table for analytics and improvement
CREATE TABLE IF NOT EXISTS search_queries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    query_text TEXT NOT NULL,
    query_type TEXT, -- funding_search, eligibility_check, etc.
    filters JSONB DEFAULT '{}',
    results_count INTEGER,
    user_feedback INTEGER CHECK (user_feedback BETWEEN 1 AND 5), -- 1-5 rating
    response_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_crawled_pages_url ON crawled_pages(url);
CREATE INDEX IF NOT EXISTS idx_crawled_pages_crawled_at ON crawled_pages(crawled_at);
CREATE INDEX IF NOT EXISTS idx_crawled_pages_embedding ON crawled_pages USING ivfflat (embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS idx_funding_programs_deadline ON funding_programs(deadline);
CREATE INDEX IF NOT EXISTS idx_funding_programs_program_type ON funding_programs(program_type);
CREATE INDEX IF NOT EXISTS idx_funding_programs_status ON funding_programs(status);

CREATE INDEX IF NOT EXISTS idx_funding_calls_deadline ON funding_calls(deadline);
CREATE INDEX IF NOT EXISTS idx_funding_calls_status ON funding_calls(status);
CREATE INDEX IF NOT EXISTS idx_funding_calls_program_id ON funding_calls(program_id);

CREATE INDEX IF NOT EXISTS idx_organizations_type ON organizations(organization_type);
CREATE INDEX IF NOT EXISTS idx_organizations_country ON organizations(country);

CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status);
CREATE INDEX IF NOT EXISTS idx_applications_date ON applications(application_date);

CREATE INDEX IF NOT EXISTS idx_search_queries_created_at ON search_queries(created_at);
CREATE INDEX IF NOT EXISTS idx_search_queries_type ON search_queries(query_type);

-- Create full-text search indexes
CREATE INDEX IF NOT EXISTS idx_crawled_pages_content_fts ON crawled_pages USING gin(to_tsvector('english', content));
CREATE INDEX IF NOT EXISTS idx_funding_programs_name_fts ON funding_programs USING gin(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_funding_programs_description_fts ON funding_programs USING gin(to_tsvector('english', description));

-- Create function for vector similarity search
CREATE OR REPLACE FUNCTION search_similar_content(
    query_embedding vector(1536),
    similarity_threshold float DEFAULT 0.7,
    max_results int DEFAULT 10
)
RETURNS TABLE (
    id uuid,
    url text,
    content text,
    metadata jsonb,
    similarity float
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cp.id,
        cp.url,
        cp.content,
        cp.metadata,
        1 - (cp.embedding <=> query_embedding) as similarity
    FROM crawled_pages cp
    WHERE cp.embedding IS NOT NULL
        AND cp.status = 'active'
        AND 1 - (cp.embedding <=> query_embedding) > similarity_threshold
    ORDER BY cp.embedding <=> query_embedding
    LIMIT max_results;
END;
$$ LANGUAGE plpgsql;

-- Create function for deadline monitoring
CREATE OR REPLACE FUNCTION get_upcoming_deadlines(
    days_ahead int DEFAULT 30
)
RETURNS TABLE (
    call_id uuid,
    program_name text,
    call_title text,
    deadline timestamp with time zone,
    days_remaining int,
    budget_available bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        fc.id,
        fp.name,
        fc.title,
        fc.deadline,
        EXTRACT(DAY FROM fc.deadline - NOW())::int,
        fc.budget_available
    FROM funding_calls fc
    JOIN funding_programs fp ON fc.program_id = fp.id
    WHERE fc.status = 'open'
        AND fc.deadline > NOW()
        AND fc.deadline <= NOW() + INTERVAL '1 day' * days_ahead
    ORDER BY fc.deadline ASC;
END;
$$ LANGUAGE plpgsql;

-- Create function for eligibility matching
CREATE OR REPLACE FUNCTION match_eligible_programs(
    org_type text,
    project_sector text DEFAULT NULL,
    budget_min bigint DEFAULT NULL,
    budget_max bigint DEFAULT NULL
)
RETURNS TABLE (
    program_id uuid,
    program_name text,
    match_score float,
    eligibility_details jsonb
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        fp.id,
        fp.name,
        CASE 
            WHEN fp.eligibility->>'organization_types' LIKE '%' || org_type || '%' THEN 1.0
            ELSE 0.5
        END as match_score,
        fp.eligibility
    FROM funding_programs fp
    WHERE fp.status = 'active'
        AND (fp.deadline IS NULL OR fp.deadline > NOW())
        AND (budget_min IS NULL OR fp.budget_max IS NULL OR fp.budget_max >= budget_min)
        AND (budget_max IS NULL OR fp.budget_min IS NULL OR fp.budget_min <= budget_max)
    ORDER BY match_score DESC;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_crawled_pages_updated_at BEFORE UPDATE ON crawled_pages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_funding_programs_updated_at BEFORE UPDATE ON funding_programs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_funding_calls_updated_at BEFORE UPDATE ON funding_calls FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_applications_updated_at BEFORE UPDATE ON applications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data for testing
INSERT INTO funding_programs (name, description, program_type, deadline, budget_min, budget_max, source_url) VALUES
('Horizon Europe - ERC Starting Grants', 'Support for excellent principal investigators at the career stage at which they are starting their own independent research team', 'horizon-europe', '2024-10-17 17:00:00+00', 1000000, 1500000, 'https://erc.europa.eu/funding/starting-grants'),
('Erasmus+ Key Action 1', 'Learning mobility of individuals in the field of education, training and youth', 'erasmus+', '2024-09-30 12:00:00+00', 50000, 500000, 'https://erasmus-plus.ec.europa.eu/programme-guide/part-b/key-action-1'),
('LIFE Environment and Resource Efficiency', 'Projects that develop, test and demonstrate policy or management approaches, best practices and solutions', 'life', '2024-11-07 17:00:00+00', 500000, 2000000, 'https://cinea.ec.europa.eu/programmes/life_en')
ON CONFLICT (source_url) DO NOTHING;

-- Create RLS (Row Level Security) policies if needed
-- ALTER TABLE crawled_pages ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE funding_programs ENABLE ROW LEVEL SECURITY;

-- Grant permissions (adjust based on your Supabase setup)
-- GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
-- GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
