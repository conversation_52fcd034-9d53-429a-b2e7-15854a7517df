# Project Structure

```
eu-funds-mcp-server/
├── src/
│   ├── crawl4ai_mcp.py          # Main MCP server
│   ├── utils.py                 # Core utilities
│   ├── eu_funds_processor.py    # EU funds specific logic
│   ├── contextual_rag.py        # Enhanced RAG system
│   └── context_engine.py        # Context engineering
├── config/
│   ├── .env.example             # Environment template
│   ├── supabase_schema.sql      # Database schema
│   └── mcp_config.json          # MCP configuration
├── tests/
│   ├── unit/                    # Unit tests
│   ├── integration/             # Integration tests
│   └── performance/             # Performance tests
├── docs/                        # Documentation
├── scripts/                     # Utility scripts
└── docker/                      # Docker files
```