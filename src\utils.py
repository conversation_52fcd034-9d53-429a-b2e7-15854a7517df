"""
Core utilities for EU Funds MCP Server
Provides SupabaseManager, RAGSystem, EmbeddingGenerator, and ContentProcessor
"""

import asyncio
import logging
import os
from typing import Dict, Any, List, Optional, Tuple
import json
from datetime import datetime, timedelta

# External imports
import openai
from supabase import create_client, Client
import numpy as np
from crawl4ai import AsyncWebCrawler
from bs4 import BeautifulSoup
import re

logger = logging.getLogger(__name__)


class SupabaseManager:
    """Manages Supabase database operations with connection pooling"""
    
    def __init__(self):
        self.url = os.getenv("SUPABASE_URL")
        self.service_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.client: Optional[Client] = None
        self.pool_size = int(os.getenv("DB_POOL_SIZE", "10"))
        
    async def initialize(self) -> None:
        """Initialize Supabase client"""
        try:
            if not self.url or not self.service_key:
                raise ValueError("Missing Supabase configuration")
                
            self.client = create_client(self.url, self.service_key)
            logger.info("Supabase client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Supabase: {str(e)}")
            raise
    
    async def store_crawled_page(
        self,
        url: str,
        content: str,
        metadata: Dict[str, Any],
        embedding: Optional[List[float]] = None
    ) -> Dict[str, Any]:
        """Store crawled page with optional embedding"""
        try:
            data = {
                "url": url,
                "content": content,
                "metadata": metadata,
                "crawled_at": datetime.utcnow().isoformat(),
                "embedding": embedding
            }
            
            result = self.client.table("crawled_pages").insert(data).execute()
            return {"success": True, "id": result.data[0]["id"]}
            
        except Exception as e:
            logger.error(f"Error storing crawled page: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def search_similar_content(
        self,
        query_embedding: List[float],
        similarity_threshold: float = 0.7,
        max_results: int = 10
    ) -> List[Dict[str, Any]]:
        """Search for similar content using vector similarity"""
        try:
            # Use Supabase vector search with pgvector
            result = self.client.rpc(
                "search_similar_content",
                {
                    "query_embedding": query_embedding,
                    "similarity_threshold": similarity_threshold,
                    "max_results": max_results
                }
            ).execute()
            
            return result.data or []
            
        except Exception as e:
            logger.error(f"Error searching similar content: {str(e)}")
            return []
    
    async def get_funding_programs(
        self,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Get funding programs with optional filters"""
        try:
            query = self.client.table("funding_programs").select("*")
            
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)
            
            result = query.execute()
            return result.data or []
            
        except Exception as e:
            logger.error(f"Error getting funding programs: {str(e)}")
            return []
    
    async def store_funding_program(
        self,
        program_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Store funding program information"""
        try:
            result = self.client.table("funding_programs").insert(program_data).execute()
            return {"success": True, "id": result.data[0]["id"]}
            
        except Exception as e:
            logger.error(f"Error storing funding program: {str(e)}")
            return {"success": False, "error": str(e)}


class EmbeddingGenerator:
    """Generates embeddings using OpenAI API"""
    
    def __init__(self):
        self.client = openai.AsyncOpenAI(
            api_key=os.getenv("OPENAI_API_KEY")
        )
        self.model = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
        self.batch_size = int(os.getenv("EMBEDDING_BATCH_SIZE", "20"))
        
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for single text"""
        try:
            response = await self.client.embeddings.create(
                model=self.model,
                input=text
            )
            return response.data[0].embedding
            
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            raise
    
    async def generate_embeddings_batch(
        self,
        texts: List[str]
    ) -> List[List[float]]:
        """Generate embeddings for multiple texts in batches"""
        try:
            embeddings = []
            
            for i in range(0, len(texts), self.batch_size):
                batch = texts[i:i + self.batch_size]
                
                response = await self.client.embeddings.create(
                    model=self.model,
                    input=batch
                )
                
                batch_embeddings = [item.embedding for item in response.data]
                embeddings.extend(batch_embeddings)
                
                # Small delay to respect rate limits
                await asyncio.sleep(0.1)
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Error generating batch embeddings: {str(e)}")
            raise


class ContentProcessor:
    """Processes and cleans web content"""
    
    def __init__(self):
        self.chunk_size = int(os.getenv("CHUNK_SIZE", "800"))
        self.chunk_overlap = int(os.getenv("CHUNK_OVERLAP", "200"))
        
    def clean_html(self, html_content: str) -> str:
        """Clean HTML and extract text content"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text and clean whitespace
            text = soup.get_text()
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except Exception as e:
            logger.error(f"Error cleaning HTML: {str(e)}")
            return html_content
    
    def extract_metadata(self, html_content: str, url: str) -> Dict[str, Any]:
        """Extract metadata from HTML content"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            metadata = {
                "url": url,
                "title": "",
                "description": "",
                "keywords": [],
                "language": "en"
            }
            
            # Extract title
            title_tag = soup.find('title')
            if title_tag:
                metadata["title"] = title_tag.get_text().strip()
            
            # Extract meta description
            desc_tag = soup.find('meta', attrs={'name': 'description'})
            if desc_tag:
                metadata["description"] = desc_tag.get('content', '').strip()
            
            # Extract keywords
            keywords_tag = soup.find('meta', attrs={'name': 'keywords'})
            if keywords_tag:
                keywords = keywords_tag.get('content', '').strip()
                metadata["keywords"] = [k.strip() for k in keywords.split(',')]
            
            # Extract language
            html_tag = soup.find('html')
            if html_tag and html_tag.get('lang'):
                metadata["language"] = html_tag.get('lang')
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting metadata: {str(e)}")
            return {"url": url, "title": "", "description": "", "keywords": [], "language": "en"}
    
    def chunk_text(self, text: str) -> List[str]:
        """Split text into semantic chunks with overlap"""
        try:
            # Simple sentence-based chunking
            sentences = re.split(r'[.!?]+', text)
            chunks = []
            current_chunk = ""
            
            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue
                
                # Check if adding sentence would exceed chunk size
                if len(current_chunk) + len(sentence) > self.chunk_size:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                        
                        # Create overlap by keeping last part of chunk
                        overlap_words = current_chunk.split()[-self.chunk_overlap//10:]
                        current_chunk = " ".join(overlap_words) + " " + sentence
                    else:
                        current_chunk = sentence
                else:
                    current_chunk += " " + sentence
            
            # Add final chunk
            if current_chunk.strip():
                chunks.append(current_chunk.strip())
            
            return chunks
            
        except Exception as e:
            logger.error(f"Error chunking text: {str(e)}")
            return [text]


class RAGSystem:
    """Retrieval-Augmented Generation system for funding opportunities"""
    
    def __init__(self, supabase_manager: SupabaseManager, embedding_generator: EmbeddingGenerator):
        self.supabase = supabase_manager
        self.embeddings = embedding_generator
        self.similarity_threshold = float(os.getenv("SIMILARITY_THRESHOLD", "0.7"))
        self.max_results = int(os.getenv("MAX_SEARCH_RESULTS", "10"))
        
    async def initialize(self) -> None:
        """Initialize RAG system"""
        logger.info("RAG system initialized")
    
    async def search_funding_opportunities(
        self,
        query: str,
        max_results: Optional[int] = None,
        similarity_threshold: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """Search for funding opportunities using hybrid search"""
        try:
            # Generate query embedding
            query_embedding = await self.embeddings.generate_embedding(query)
            
            # Search similar content
            results = await self.supabase.search_similar_content(
                query_embedding=query_embedding,
                similarity_threshold=similarity_threshold or self.similarity_threshold,
                max_results=max_results or self.max_results
            )
            
            # Enhance results with funding program data
            enhanced_results = []
            for result in results:
                enhanced_result = {
                    "content": result.get("content", ""),
                    "url": result.get("url", ""),
                    "title": result.get("metadata", {}).get("title", ""),
                    "similarity_score": result.get("similarity", 0.0),
                    "metadata": result.get("metadata", {})
                }
                enhanced_results.append(enhanced_result)
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"Error searching funding opportunities: {str(e)}")
            return []
