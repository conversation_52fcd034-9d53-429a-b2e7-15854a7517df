"""
EU Funds MCP Server - Main server implementation
Provides MCP tools for crawling and analyzing EU funding opportunities
"""

import asyncio
import logging
import os
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

# MCP imports
from mcp import McpServer
from mcp.server import mcp
from mcp.types import Tool

# Local imports
from .utils import (
    SupabaseManager,
    RAGSystem, 
    EmbeddingGenerator,
    ContentProcessor
)
from .eu_funds_processor import EUFundsProcessor

# Load environment variables
load_dotenv(dotenv_path="config/.env")

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO")),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Initialize core components
supabase_manager = SupabaseManager()
embedding_generator = EmbeddingGenerator()
content_processor = ContentProcessor()
rag_system = RAGSystem(supabase_manager, embedding_generator)
eu_funds_processor = EUFundsProcessor(supabase_manager, rag_system, content_processor)

# Create MCP server instance
server = McpServer(
    name=os.getenv("MCP_SERVER_NAME", "eu-funds-mcp"),
    version=os.getenv("MCP_SERVER_VERSION", "1.0.0")
)


@mcp.tool()
async def crawl_eu_funds_site(
    url: str,
    max_pages: int = 10,
    include_patterns: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Crawl EU funding website and extract funding opportunities
    
    Args:
        url: Base URL to start crawling from
        max_pages: Maximum number of pages to crawl
        include_patterns: URL patterns to include (regex)
        
    Returns:
        Dict with success status, crawled pages count, and extracted data
    """
    try:
        logger.info(f"Starting crawl of EU funds site: {url}")
        
        result = await eu_funds_processor.crawl_funding_site(
            url=url,
            max_pages=max_pages,
            include_patterns=include_patterns or []
        )
        
        logger.info(f"Crawl completed: {result.get('pages_crawled', 0)} pages")
        return {
            "success": True,
            "pages_crawled": result.get("pages_crawled", 0),
            "funding_programs": result.get("funding_programs", []),
            "active_calls": result.get("active_calls", []),
            "message": f"Successfully crawled {result.get('pages_crawled', 0)} pages"
        }
        
    except Exception as e:
        logger.error(f"Error crawling EU funds site: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to crawl EU funds site"
        }


@mcp.tool()
async def extract_funding_programs(
    content: str,
    source_url: str
) -> Dict[str, Any]:
    """
    Extract structured funding program information from content
    
    Args:
        content: Raw HTML or text content
        source_url: Source URL for attribution
        
    Returns:
        Dict with extracted funding programs and metadata
    """
    try:
        logger.info(f"Extracting funding programs from: {source_url}")
        
        programs = await eu_funds_processor.extract_funding_programs(
            content=content,
            source_url=source_url
        )
        
        return {
            "success": True,
            "programs_found": len(programs),
            "programs": programs,
            "source_url": source_url,
            "message": f"Extracted {len(programs)} funding programs"
        }
        
    except Exception as e:
        logger.error(f"Error extracting funding programs: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to extract funding programs"
        }


@mcp.tool()
async def analyze_active_calls(
    query: Optional[str] = None,
    deadline_days: int = 90
) -> Dict[str, Any]:
    """
    Analyze active funding calls with optional filtering
    
    Args:
        query: Optional search query to filter calls
        deadline_days: Only include calls with deadlines within this many days
        
    Returns:
        Dict with active calls analysis and recommendations
    """
    try:
        logger.info(f"Analyzing active calls with query: {query}")
        
        analysis = await eu_funds_processor.analyze_active_calls(
            query=query,
            deadline_days=deadline_days
        )
        
        return {
            "success": True,
            "active_calls": analysis.get("calls", []),
            "total_found": len(analysis.get("calls", [])),
            "upcoming_deadlines": analysis.get("upcoming_deadlines", []),
            "recommendations": analysis.get("recommendations", []),
            "message": f"Found {len(analysis.get('calls', []))} active calls"
        }
        
    except Exception as e:
        logger.error(f"Error analyzing active calls: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to analyze active calls"
        }


@mcp.tool()
async def search_funding_opportunities(
    query: str,
    max_results: int = 10,
    similarity_threshold: float = 0.7
) -> Dict[str, Any]:
    """
    Search for funding opportunities using hybrid RAG search
    
    Args:
        query: Search query describing funding needs
        max_results: Maximum number of results to return
        similarity_threshold: Minimum similarity score for results
        
    Returns:
        Dict with search results and relevance scores
    """
    try:
        logger.info(f"Searching funding opportunities for: {query}")
        
        results = await rag_system.search_funding_opportunities(
            query=query,
            max_results=max_results,
            similarity_threshold=similarity_threshold
        )
        
        return {
            "success": True,
            "query": query,
            "results_found": len(results),
            "opportunities": results,
            "message": f"Found {len(results)} relevant funding opportunities"
        }
        
    except Exception as e:
        logger.error(f"Error searching funding opportunities: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to search funding opportunities"
        }


@mcp.tool()
async def check_eligibility(
    project_description: str,
    organization_type: str,
    budget_range: Optional[str] = None
) -> Dict[str, Any]:
    """
    Check eligibility for funding programs based on project details
    
    Args:
        project_description: Description of the project seeking funding
        organization_type: Type of organization (NGO, university, company, etc.)
        budget_range: Expected budget range
        
    Returns:
        Dict with eligibility analysis and matching programs
    """
    try:
        logger.info(f"Checking eligibility for {organization_type} project")
        
        eligibility = await eu_funds_processor.check_eligibility(
            project_description=project_description,
            organization_type=organization_type,
            budget_range=budget_range
        )
        
        return {
            "success": True,
            "eligible_programs": eligibility.get("eligible", []),
            "partially_eligible": eligibility.get("partial", []),
            "recommendations": eligibility.get("recommendations", []),
            "next_steps": eligibility.get("next_steps", []),
            "message": f"Found {len(eligibility.get('eligible', []))} eligible programs"
        }
        
    except Exception as e:
        logger.error(f"Error checking eligibility: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to check eligibility"
        }


@mcp.tool()
async def monitor_deadlines(
    days_ahead: int = 30
) -> Dict[str, Any]:
    """
    Monitor upcoming funding deadlines
    
    Args:
        days_ahead: Number of days to look ahead for deadlines
        
    Returns:
        Dict with upcoming deadlines and alerts
    """
    try:
        logger.info(f"Monitoring deadlines for next {days_ahead} days")
        
        deadlines = await eu_funds_processor.monitor_deadlines(
            days_ahead=days_ahead
        )
        
        return {
            "success": True,
            "upcoming_deadlines": deadlines.get("deadlines", []),
            "urgent_alerts": deadlines.get("urgent", []),
            "total_monitored": len(deadlines.get("deadlines", [])),
            "message": f"Monitoring {len(deadlines.get('deadlines', []))} upcoming deadlines"
        }
        
    except Exception as e:
        logger.error(f"Error monitoring deadlines: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to monitor deadlines"
        }


async def main():
    """Main server entry point"""
    try:
        logger.info("Starting EU Funds MCP Server...")
        
        # Initialize components
        await supabase_manager.initialize()
        await rag_system.initialize()
        
        logger.info("EU Funds MCP Server started successfully")
        
        # Run the server
        await server.run()
        
    except Exception as e:
        logger.error(f"Failed to start server: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
