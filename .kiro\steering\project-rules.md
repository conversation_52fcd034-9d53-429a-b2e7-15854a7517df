---
inclusion: always
---

# Основни правила за проекта

## 🎯 Архитектура на проекта
- **MCP Server структура**: Това е MCP (Model Context Protocol) сървър за web crawling и RAG функционалност
- **Основни компоненти**: WebCrawler, SupabaseManager, RAGSystem, EmbeddingGenerator, ContentProcessor в `src/utils.py`
- **Главен сървър**: `src/crawl4ai_mcp.py` имплементира MCP tools и server lifecycle
- **Environment конфигурация**: Всички API ключове и настройки в `config/.env` (никога не commit-вай този файл)

## 🔧 Организация на кода
- **Лимит на файл**: Максимум 500 реда на файл - раздели на модули ако се надвиши
- **Модулна структура**: Групирай свързана функционалност в `src/` директорията
- **Import конвенции**: Използвай relative imports в проекта, absolute за външни библиотеки
- **Async модел**: Всички I/O операции трябва да са async/await - това е async MCP сървър

## 🛠️ MCP Tool разработка
- **Tool подпис**: Използвай `@mcp.tool()` decorator с правилни type hints
- **Return формат**: Винаги връщай `Dict[str, Any]` с `success: bool` поле
- **Error handling**: Обгради в try/catch, връщай структурирани error отговори
- **Logging**: Използвай `logger.info/error` за всички операции

## 🗄️ Database интеграция
- **Само Supabase**: Използвай SupabaseManager за всички database операции
- **Vector съхранение**: Съхранявай embeddings в `crawled_pages` таблица с pgvector
- **Batch операции**: Използвай batch методи за множество записи
- **SQL файлове**: Пази schema промени в `config/supabase_schema.sql`

## 🕷️ Web Crawling правила
- **Rate Limiting**: Спазвай `CRAWL_DELAY` environment variable
- **Robots.txt**: Винаги проверявай и спазвай robots.txt
- **Error Recovery**: Обработвай HTTP грешки грациозно, продължи с други страници
- **Content Processing**: Почиствай HTML, извличай текст, запазвай код блокове

## 🧠 RAG имплементация
- **Embedding модел**: Използвай OpenAI text-embedding-3-small по подразбиране
- **Chunking стратегия**: Semantic chunking с 10-20% overlap
- **Search методи**: Комбинирай vector similarity с keyword search
- **Source attribution**: Винаги включвай source URLs в отговорите

## 🔒 Сигурност и Environment
- **API ключове**: Никога не hardcode - използвай само environment variables
- **Input валидация**: Валидирай всички потребителски входове, особено URLs
- **SQL безопасност**: Използвай parameterized queries чрез Supabase client
- **Error съобщения**: Не разкривай вътрешни детайли в error отговори

## 🧪 Testing изисквания
- **Нови функции**: Пиши тестове за всеки нов MCP tool
- **Test структура**: Използвай pytest с async поддръжка
- **Test категории**: Unit тестове за utils, integration тестове за MCP tools
- **Mock външни**: Mock-вай OpenAI и Supabase calls в тестовете

## 📝 Документация стандарти
- **Docstrings**: Всяка функция се нуждае от подробен docstring с Args/Returns
- **Type Hints**: Пълни type annotations са задължителни
- **README актуализации**: Актуализирай README.md при добавяне на нови tools или функции
- **Code коментари**: Обяснявай сложна логика, не очевидни операции

## 🚀 Development workflow
- **Environment setup**: Използвай `start_server.py` за разработка
- **Transport режими**: Поддържай и stdio и SSE transports
- **Конфигурация**: Провери `config/.env.example` за нужните променливи
- **Dependencies**: Поддържай `requirements.txt` актуален с точни версии

## ⚡ Performance насоки
- **Batch обработка**: Обработвай множество елементи заедно когато е възможно
- **Connection pooling**: Преизползвай database връзки
- **Memory управление**: Почиствай големи обекти след обработка
- **Concurrent операции**: Използвай asyncio.gather за паралелни задачи

## 🎯 AI Assistant поведение
- **Context осведоменост**: Винаги чети project файловете преди промени
- **Инкрементално развитие**: Прави малки, тестваеми промени
- **Error възстановяване**: Ако нещо се провали, обясни и опитай алтернативен подход
- **Code качество**: Приоритизирай четим, поддържаем код пред умни решения