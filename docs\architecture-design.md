# 🏗️ EU Funds MCP Server - Architecture Design

## 🎯 System Overview

### Core Purpose
Specialized MCP server for extracting, processing, and providing structured access to EU funding information from eufunds.bg and related sources, with advanced RAG capabilities and context engineering.

### Key Components
- **MCP Server Core** - Protocol implementation and tool management
- **EU Funds Processor** - Domain-specific content extraction and analysis
- **Context Engine** - Advanced context engineering and reasoning
- **RAG System** - Hybrid search with contextual embeddings
- **Crawling Engine** - Ethical web crawling with content validation
- **Database Layer** - Supabase with pgvector for efficient storage

## 🏛️ Architecture Layers

### 1. Presentation Layer (MCP Interface)
```
┌─────────────────────────────────────┐
│           MCP Tools API             │
├─────────────────────────────────────┤
│ • crawl_eu_funds_site              │
│ • extract_funding_programs         │
│ • analyze_active_calls             │
│ • search_funding_opportunities     │
│ • check_eligibility                │
│ • monitor_deadlines                │
└─────────────────────────────────────┘
```

### 2. Business Logic Layer
```
┌─────────────────────────────────────┐
│        EU Funds Processor           │
├─────────────────────────────────────┤
│ • Program extraction               │
│ • Call analysis                    │
│ • Eligibility checking             │
│ • Deadline monitoring              │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│         Context Engine              │
├─────────────────────────────────────┤
│ • Multi-step reasoning             │
│ • Context-aware retrieval          │
│ • Domain knowledge integration     │
│ • Hallucination prevention         │
└─────────────────────────────────────┘
```

### 3. Data Processing Layer
```
┌─────────────────────────────────────┐
│           RAG System                │
├─────────────────────────────────────┤
│ • Hybrid search (vector + keyword) │
│ • Contextual embeddings            │
│ • Reranking & relevance scoring    │
│ • Source attribution              │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│        Crawling Engine              │
├─────────────────────────────────────┤
│ • Ethical crawling (robots.txt)    │
│ • Content extraction & validation  │
│ • Semantic chunking                │
│ • Deduplication                    │
└─────────────────────────────────────┘
```

### 4. Data Layer
```
┌─────────────────────────────────────┐
│      Supabase + pgvector            │
├─────────────────────────────────────┤
│ • Vector embeddings storage        │
│ • Structured data (programs, calls)│
│ • Metadata and relationships       │
│ • Full-text search indexes         │
└─────────────────────────────────────┘
```

## 🔄 Data Flow Architecture

### 1. Crawling Flow
```
Web Sources → Content Extraction → Validation → Chunking → Embeddings → Storage
     ↓              ↓                ↓           ↓           ↓          ↓
  eufunds.bg    HTML Cleaning    Quality Check  Semantic   OpenAI    Supabase
  Related sites  Text Extraction  Deduplication  Chunking   API       pgvector
```

### 2. RAG Query Flow
```
User Query → Query Analysis → Hybrid Search → Reranking → Context Assembly → Response
     ↓            ↓              ↓             ↓              ↓            ↓
  MCP Tool    Intent Detection  Vector+Keyword  Relevance   Context      Structured
  Request     Context Enrichment   Search       Scoring     Engineering   JSON Response
```

### 3. Context Engineering Flow
```
Raw Results → Domain Knowledge → Multi-step Reasoning → Validation → Final Context
     ↓              ↓                    ↓                ↓            ↓
  Search Results  EU Funds Rules    Logical Inference   Fact Check   Enhanced
  Multiple Sources Program Logic    Chain of Thought    Source Verify Context
```

## 🗂️ File Structure

```
eu-funds-mcp-server/
├── src/
│   ├── eu_funds_mcp.py          # Main MCP server (< 500 lines)
│   ├── utils.py                 # Core utilities (< 500 lines)
│   ├── eu_funds_processor.py    # EU funds logic (< 500 lines)
│   ├── context_engine.py        # Context engineering (< 500 lines)
│   ├── crawling_engine.py       # Web crawling (< 500 lines)
│   └── rag_system.py           # RAG implementation (< 500 lines)
├── config/
│   ├── .env.example            # Environment template
│   ├── supabase_schema.sql     # Database schema
│   └── mcp_config.json         # MCP configuration
├── tests/
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   └── performance/            # Performance tests
├── docs/                       # All documentation
├── scripts/                    # Utility scripts
└── docker/                     # Containerization
```

## 🔧 Technology Stack

### Core Technologies
- **Python 3.11+** - Main language with async/await
- **MCP SDK** - Model Context Protocol implementation
- **Crawl4AI** - Advanced web crawling capabilities
- **Supabase** - Database with pgvector for embeddings
- **OpenAI API** - Embeddings and potential reranking

### Key Libraries
- **asyncio** - Asynchronous programming
- **aiohttp** - Async HTTP client
- **pydantic** - Data validation and serialization
- **sqlalchemy** - Database ORM (async)
- **beautifulsoup4** - HTML parsing
- **sentence-transformers** - Backup embedding option

## 🔌 Integration Points

### External APIs
- **OpenAI API** - text-embedding-3-small for embeddings
- **Supabase API** - Database operations and vector search
- **EU Funds APIs** - If available for validation

### MCP Ecosystem
- **Claude Desktop** - Primary client integration
- **Other MCP Clients** - Generic MCP protocol support
- **File System MCP** - Document storage integration
- **Web Search MCP** - Information validation

## 🚀 Deployment Architecture

### Development Environment
```
Local Machine → Python Virtual Env → MCP Server → SSE Transport → Claude Desktop
```

### Production Environment
```
Docker Container → Load Balancer → MCP Server Instances → Supabase Cluster
```

### Scaling Considerations
- **Horizontal scaling** - Multiple server instances
- **Database scaling** - Supabase connection pooling
- **Caching layer** - Redis for frequent queries
- **CDN integration** - Static content delivery

## 🔒 Security Architecture

### Authentication & Authorization
- **Environment-based secrets** - No hardcoded credentials
- **API key rotation** - Regular credential updates
- **Rate limiting** - Prevent abuse and overuse
- **Input validation** - Sanitize all user inputs

### Data Protection
- **Encryption at rest** - Supabase native encryption
- **Encryption in transit** - HTTPS/TLS for all communications
- **Data anonymization** - Remove PII from crawled content
- **Audit logging** - Track all operations

## 📊 Performance Architecture

### Response Time Targets
- **RAG queries**: < 2 seconds end-to-end
- **Crawling operations**: > 10 pages/minute
- **Embedding generation**: < 500ms per chunk
- **Database queries**: < 100ms average

### Optimization Strategies
- **Connection pooling** - Efficient database connections
- **Batch processing** - Group operations for efficiency
- **Async operations** - Non-blocking I/O throughout
- **Caching** - Intelligent result caching

## 🔄 Error Handling Architecture

### Error Categories
- **Network errors** - HTTP timeouts, connection failures
- **API errors** - OpenAI rate limits, Supabase issues
- **Data errors** - Invalid content, parsing failures
- **Logic errors** - Business rule violations

### Recovery Strategies
- **Graceful degradation** - Partial functionality on errors
- **Retry mechanisms** - Exponential backoff for transient errors
- **Circuit breakers** - Prevent cascade failures
- **Fallback options** - Alternative data sources

## 📈 Monitoring & Observability

### Key Metrics
- **Response times** - Track performance trends
- **Error rates** - Monitor system health
- **Resource usage** - CPU, memory, database connections
- **Business metrics** - Crawling success, RAG quality

### Logging Strategy
- **Structured logging** - JSON format for analysis
- **Log levels** - DEBUG, INFO, WARNING, ERROR
- **Correlation IDs** - Track requests across components
- **Performance logging** - Timing and resource usage

---

*This architecture supports the full EU Funds MCP server requirements*  
*All components designed for scalability, maintainability, and performance*