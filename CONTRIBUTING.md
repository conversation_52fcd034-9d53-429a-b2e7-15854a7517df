# 🤝 Contributing Guidelines

## 🏗️ Development Setup
1. Clone repository
2. Create virtual environment: `python -m venv venv`
3. Install dependencies: `pip install -r requirements.txt`
4. Copy environment: `cp config/.env.example config/.env`
5. Configure API keys in `config/.env`

## 📏 Code Standards
- **File limit**: Max 500 lines per file
- **Type hints**: Required on all functions
- **Async/await**: All I/O operations must be async
- **Error handling**: Comprehensive try/catch blocks
- **Logging**: Use structured logging throughout

## 🧪 Testing Requirements
- Unit tests for all utilities
- Integration tests for MCP tools
- Performance benchmarks
- Error scenario coverage

## 📋 Pull Request Process
1. Check `docs/project-status.md` for current state
2. Follow `docs/validation-checklist.md`
3. Update documentation
4. Ensure all tests pass
5. Update project status

## 🚨 Quality Gates
Every contribution must pass ALL validation criteria in `docs/validation-checklist.md`

