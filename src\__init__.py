"""
EU Funds MCP Server Package
Provides MCP tools for crawling and analyzing EU funding opportunities
"""

__version__ = "1.0.0"
__author__ = "EU Funds MCP Team"
__description__ = "MCP Server for EU funding opportunities analysis and RAG"

# Import main components for easy access
from .eu_funds_mcp import server, main
from .utils import (
    SupabaseManager,
    RAGSystem,
    EmbeddingGenerator,
    ContentProcessor
)
from .eu_funds_processor import EUFundsProcessor
from .context_engine import ContextEngine

__all__ = [
    "server",
    "main",
    "SupabaseManager",
    "RAGSystem", 
    "EmbeddingGenerator",
    "ContentProcessor",
    "EUFundsProcessor",
    "ContextEngine"
]
