# 🇪🇺 EU Funds MCP Server

Specialized MCP server for intelligent access to EU funding information with advanced RAG capabilities.

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Supabase account
- OpenAI API key

### Installation
```bash
git clone <repository>
cd eu-funds-mcp-server
pip install -r requirements.txt
cp config/.env.example config/.env
# Edit config/.env with your API keys
python start_server.py
```

### MCP Tools
- `crawl_eu_funds_site` - Crawl EU funding websites
- `extract_funding_programs` - Extract program information
- `analyze_active_calls` - Analyze funding opportunities
- `search_funding_opportunities` - Intelligent search
- `check_eligibility` - Eligibility assessment
- `monitor_deadlines` - Deadline tracking

## 📚 Documentation
- [Architecture Design](docs/architecture-design.md)
- [API Documentation](docs/api-documentation.md)
- [Deployment Guide](docs/deployment-guide.md)
- [Development Tasks](docs/development-tasks.md)

## 🔧 Development
See [project-status.md](docs/project-status.md) for current progress.



