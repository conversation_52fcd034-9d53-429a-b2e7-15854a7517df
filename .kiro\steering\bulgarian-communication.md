---
inclusion: always
---

# Communication & Language Standards

## 🗣️ Language Requirements
- **Bulgarian responses**: When user writes in Bulgarian, respond in Bulgarian
- **English responses**: When user writes in English, respond in English  
- **Code elements**: Always use English for variables, functions, comments, and error messages
- **Documentation**: Follow the language of existing project documentation

## 💬 Communication Style
- **Direct and clear**: Focus on solutions without unnecessary explanations
- **Technical precision**: Use correct terminology and be specific
- **Practical approach**: Provide concrete examples and working code
- **Professional tone**: Maintain consistent professional communication

## 🎯 Response Guidelines
- **Concise answers**: Give direct responses for simple questions
- **Detailed explanations**: Provide comprehensive information for complex topics
- **Code examples**: Always include working code snippets when relevant
- **Context awareness**: Explain the reasoning behind technical decisions

## 🔧 Technical Communication Standards
- **Identifiers**: All variable names, function names, and class names in English
- **Error handling**: Error messages and logs in English for consistency
- **Comments**: Code comments in English for international compatibility
- **API responses**: Structure responses consistently with `success` boolean and clear data/error fields

## 📝 Documentation Conventions
- **README files**: Match the language of the target audience
- **Code documentation**: Docstrings and inline comments in English
- **User-facing content**: Match user's preferred language
- **Technical specifications**: English for consistency across development team