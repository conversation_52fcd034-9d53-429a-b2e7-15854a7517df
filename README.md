# 🇪🇺 EU Funds MCP Server

An advanced Model Context Protocol (MCP) server for crawling, analyzing, and providing intelligent access to EU funding opportunities through RAG (Retrieval-Augmented Generation) technology.

## 🎯 Overview

This MCP server provides comprehensive tools for:
- **Web Crawling**: Intelligent crawling of EU funding websites
- **Content Processing**: Advanced extraction and semantic chunking
- **Vector Search**: Hybrid search using embeddings and keywords  
- **RAG Analysis**: Context-aware funding opportunity matching
- **Deadline Monitoring**: Automated tracking of application deadlines
- **Eligibility Checking**: AI-powered eligibility analysis

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- Supabase account with pgvector enabled
- OpenAI API key

### Installation

1. **Clone and setup**:
```bash
git clone <repository-url>
cd eu-funds-mcp-server
pip install -r requirements.txt
```

2. **Configure environment**:
```bash
cp config/.env.example config/.env
# Edit config/.env with your API keys
```

3. **Setup database**:
```bash
# Run the schema in your Supabase SQL editor
cat config/supabase_schema.sql
```

4. **Start the server**:
```bash
python start_server.py
```

## 🛠️ MCP Tools

### Core Crawling Tools

#### `crawl_eu_funds_site`
Crawl EU funding websites and extract opportunities.

```python
{
    "url": "https://ec.europa.eu/info/funding-tenders/opportunities/portal/screen/programmes",
    "max_pages": 10,
    "include_patterns": ["call", "funding", "grant"]
}
```

#### `extract_funding_programs`
Extract structured funding program information from content.

```python
{
    "content": "<html content>",
    "source_url": "https://example.eu/funding-call"
}
```

### Analysis Tools

#### `search_funding_opportunities`
Search for funding opportunities using hybrid RAG search.

```python
{
    "query": "digital transformation SME funding",
    "max_results": 10,
    "similarity_threshold": 0.7
}
```

#### `analyze_active_calls`
Analyze active funding calls with filtering and recommendations.

```python
{
    "query": "artificial intelligence research",
    "deadline_days": 90
}
```

#### `check_eligibility`
Check eligibility for funding programs based on project details.

```python
{
    "project_description": "AI-powered healthcare solution for rural areas",
    "organization_type": "SME",
    "budget_range": "500000-1000000"
}
```

#### `monitor_deadlines`
Monitor upcoming funding deadlines and get alerts.

```python
{
    "days_ahead": 30
}
```

## 🏗️ Architecture

### Core Components

- **`src/eu_funds_mcp.py`**: Main MCP server with tool definitions
- **`src/utils.py`**: Core utilities (SupabaseManager, RAGSystem, etc.)
- **`src/eu_funds_processor.py`**: EU-specific processing logic
- **`src/context_engine.py`**: Advanced context engineering patterns

### Database Schema

The system uses Supabase PostgreSQL with pgvector for:
- **crawled_pages**: Crawled content with embeddings
- **funding_programs**: Structured funding program data
- **funding_calls**: Specific call instances
- **organizations**: Organization types and success rates
- **applications**: Application history tracking

### RAG Pipeline

1. **Content Ingestion**: Web crawling with Crawl4AI
2. **Processing**: HTML cleaning and semantic chunking
3. **Embedding**: OpenAI text-embedding-3-small
4. **Storage**: Supabase with pgvector indexing
5. **Retrieval**: Hybrid vector + keyword search
6. **Generation**: Context-aware responses with OpenAI

## 🔧 Configuration

### Environment Variables

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
MODEL_CHOICE=gpt-4o-mini

# Supabase Configuration  
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Crawling Configuration
CRAWL_DELAY=1.0
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=30

# RAG Configuration
EMBEDDING_MODEL=text-embedding-3-small
CHUNK_SIZE=800
CHUNK_OVERLAP=200
MAX_SEARCH_RESULTS=10
SIMILARITY_THRESHOLD=0.7

# Server Configuration
MCP_SERVER_NAME=eu-funds-mcp
LOG_LEVEL=INFO
```

### MCP Client Configuration

Add to your MCP client configuration:

```json
{
  "mcpServers": {
    "eu-funds-mcp": {
      "command": "python",
      "args": ["start_server.py"],
      "cwd": "/path/to/eu-funds-mcp-server"
    }
  }
}
```

## 🧪 Testing

Run the test suite:

```bash
# Unit tests
pytest tests/unit/

# Integration tests  
pytest tests/integration/

# All tests
pytest
```

## 📊 Performance

- **RAG Queries**: < 2 seconds average response time
- **Crawling**: > 10 pages/minute with rate limiting
- **Embeddings**: < 500ms per chunk generation
- **Database**: < 100ms average query time

## 🔒 Security

- Environment variables for all sensitive data
- Input validation on all user inputs
- Parameterized database queries
- Rate limiting for external APIs
- Error message sanitization

## 🌐 Language Support

- **Bulgarian**: Responds in Bulgarian for Bulgarian input
- **English**: Responds in English for English input
- **Code**: All code elements in English
- **Technical Terms**: Consistent terminology

## 📚 Documentation

- **API Documentation**: `docs/api-documentation.md`
- **Architecture Design**: `docs/architecture-design.md`
- **User Guide**: `docs/user-guide.md`
- **Deployment Guide**: `docs/deployment-guide.md`

## 🤝 Contributing

1. Follow the project rules in `.kiro/steering/project-rules.md`
2. Maximum 500 lines per file
3. Full type hints and docstrings required
4. Async/await for all I/O operations
5. Comprehensive error handling

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation in `docs/`
- Review validation checklist in `docs/validation-checklist.md`
- Follow development tasks in `docs/development-tasks.md`

---

**Status**: ✅ Project structure setup complete  
**Next**: Environment configuration and basic MCP server testing  
**Version**: 1.0.0
