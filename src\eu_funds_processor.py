"""
EU Funds Processor - Specialized logic for EU funding opportunities
Handles crawling, extraction, and analysis of EU funding data
"""

import asyncio
import logging
import re
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse

# External imports
from crawl4ai import AsyncWebCrawler
import openai

# Local imports
from .utils import SupabaseManager, RAGSystem, ContentProcessor

logger = logging.getLogger(__name__)


class EUFundsProcessor:
    """Processes EU funding opportunities with specialized extraction logic"""
    
    def __init__(
        self,
        supabase_manager: SupabaseManager,
        rag_system: RAGSystem,
        content_processor: ContentProcessor
    ):
        self.supabase = supabase_manager
        self.rag = rag_system
        self.processor = content_processor
        self.openai_client = openai.AsyncOpenAI()
        
        # EU funding specific patterns
        self.funding_patterns = {
            "program_name": r"(?i)(horizon\s+europe|erasmus\+|interreg|life|digital\s+europe|creative\s+europe)",
            "deadline": r"(?i)(deadline|closing\s+date|submission\s+date)[\s:]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})",
            "budget": r"(?i)(budget|funding|grant)[\s:]*€?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(million|thousand|k|m)?",
            "eligibility": r"(?i)(eligible|eligibility)[\s:]*([^.]+)",
            "call_id": r"(?i)(call\s+id|identifier)[\s:]*([A-Z0-9\-]+)"
        }
    
    async def crawl_funding_site(
        self,
        url: str,
        max_pages: int = 10,
        include_patterns: List[str] = None
    ) -> Dict[str, Any]:
        """Crawl EU funding website and extract opportunities"""
        try:
            logger.info(f"Starting crawl of {url} with max_pages={max_pages}")
            
            crawled_pages = []
            funding_programs = []
            active_calls = []
            
            async with AsyncWebCrawler(verbose=True) as crawler:
                # Crawl main page first
                result = await crawler.arun(url=url)
                
                if result.success:
                    # Process main page
                    page_data = await self._process_crawled_page(
                        url=url,
                        html_content=result.html,
                        markdown_content=result.markdown
                    )
                    
                    crawled_pages.append(page_data)
                    
                    # Extract funding info from main page
                    programs = await self._extract_funding_info(
                        content=result.markdown,
                        source_url=url
                    )
                    funding_programs.extend(programs.get("programs", []))
                    active_calls.extend(programs.get("calls", []))
                    
                    # Find additional pages to crawl
                    additional_urls = self._extract_funding_urls(
                        html_content=result.html,
                        base_url=url,
                        include_patterns=include_patterns or []
                    )
                    
                    # Crawl additional pages
                    for i, additional_url in enumerate(additional_urls[:max_pages-1]):
                        try:
                            logger.info(f"Crawling additional page {i+1}: {additional_url}")
                            
                            additional_result = await crawler.arun(url=additional_url)
                            
                            if additional_result.success:
                                page_data = await self._process_crawled_page(
                                    url=additional_url,
                                    html_content=additional_result.html,
                                    markdown_content=additional_result.markdown
                                )
                                
                                crawled_pages.append(page_data)
                                
                                # Extract funding info
                                programs = await self._extract_funding_info(
                                    content=additional_result.markdown,
                                    source_url=additional_url
                                )
                                funding_programs.extend(programs.get("programs", []))
                                active_calls.extend(programs.get("calls", []))
                            
                            # Respect crawl delay
                            await asyncio.sleep(1.0)
                            
                        except Exception as e:
                            logger.error(f"Error crawling {additional_url}: {str(e)}")
                            continue
            
            return {
                "pages_crawled": len(crawled_pages),
                "funding_programs": funding_programs,
                "active_calls": active_calls,
                "crawled_pages": crawled_pages
            }
            
        except Exception as e:
            logger.error(f"Error crawling funding site: {str(e)}")
            raise
    
    async def extract_funding_programs(
        self,
        content: str,
        source_url: str
    ) -> List[Dict[str, Any]]:
        """Extract structured funding program information"""
        try:
            logger.info(f"Extracting funding programs from {source_url}")
            
            # Use OpenAI to extract structured information
            prompt = f"""
            Extract funding program information from the following content.
            Return a JSON array of funding programs with these fields:
            - name: Program name
            - description: Brief description
            - deadline: Application deadline (if mentioned)
            - budget: Available budget (if mentioned)
            - eligibility: Eligibility criteria
            - call_id: Call identifier (if mentioned)
            - source_url: {source_url}
            
            Content:
            {content[:4000]}  # Limit content to avoid token limits
            """
            
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            # Parse response and validate
            import json
            try:
                programs = json.loads(response.choices[0].message.content)
                if not isinstance(programs, list):
                    programs = [programs]
                
                # Store in database
                for program in programs:
                    program["source_url"] = source_url
                    program["extracted_at"] = datetime.utcnow().isoformat()
                    await self.supabase.store_funding_program(program)
                
                return programs
                
            except json.JSONDecodeError:
                logger.error("Failed to parse OpenAI response as JSON")
                return []
            
        except Exception as e:
            logger.error(f"Error extracting funding programs: {str(e)}")
            return []
    
    async def analyze_active_calls(
        self,
        query: Optional[str] = None,
        deadline_days: int = 90
    ) -> Dict[str, Any]:
        """Analyze active funding calls"""
        try:
            # Get funding programs from database
            filters = {}
            if query:
                # This would need to be implemented as a full-text search
                pass
            
            programs = await self.supabase.get_funding_programs(filters)
            
            # Filter by deadline
            cutoff_date = datetime.utcnow() + timedelta(days=deadline_days)
            active_calls = []
            upcoming_deadlines = []
            
            for program in programs:
                deadline_str = program.get("deadline")
                if deadline_str:
                    try:
                        # Parse deadline (this would need more robust date parsing)
                        deadline = datetime.fromisoformat(deadline_str.replace('Z', '+00:00'))
                        
                        if deadline > datetime.utcnow():
                            active_calls.append(program)
                            
                            if deadline <= cutoff_date:
                                upcoming_deadlines.append({
                                    "program": program,
                                    "deadline": deadline,
                                    "days_remaining": (deadline - datetime.utcnow()).days
                                })
                    except:
                        # If date parsing fails, include anyway
                        active_calls.append(program)
            
            # Generate recommendations using RAG
            recommendations = []
            if query:
                rag_results = await self.rag.search_funding_opportunities(query)
                recommendations = [
                    f"Consider {result['title']} - {result['content'][:200]}..."
                    for result in rag_results[:3]
                ]
            
            return {
                "calls": active_calls,
                "upcoming_deadlines": upcoming_deadlines,
                "recommendations": recommendations
            }
            
        except Exception as e:
            logger.error(f"Error analyzing active calls: {str(e)}")
            raise
    
    async def check_eligibility(
        self,
        project_description: str,
        organization_type: str,
        budget_range: Optional[str] = None
    ) -> Dict[str, Any]:
        """Check eligibility for funding programs"""
        try:
            # Get all funding programs
            programs = await self.supabase.get_funding_programs()
            
            # Use OpenAI to analyze eligibility
            prompt = f"""
            Analyze eligibility for EU funding programs based on:
            
            Project: {project_description}
            Organization: {organization_type}
            Budget: {budget_range or 'Not specified'}
            
            Available programs: {str(programs[:10])}  # Limit for token efficiency
            
            Return JSON with:
            - eligible: List of fully eligible programs
            - partial: List of partially eligible programs
            - recommendations: List of specific recommendations
            - next_steps: List of suggested next steps
            """
            
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            import json
            try:
                result = json.loads(response.choices[0].message.content)
                return result
            except json.JSONDecodeError:
                return {
                    "eligible": [],
                    "partial": [],
                    "recommendations": ["Unable to analyze eligibility at this time"],
                    "next_steps": ["Please review programs manually"]
                }
            
        except Exception as e:
            logger.error(f"Error checking eligibility: {str(e)}")
            raise
    
    async def monitor_deadlines(
        self,
        days_ahead: int = 30
    ) -> Dict[str, Any]:
        """Monitor upcoming funding deadlines"""
        try:
            programs = await self.supabase.get_funding_programs()
            
            cutoff_date = datetime.utcnow() + timedelta(days=days_ahead)
            deadlines = []
            urgent = []
            
            for program in programs:
                deadline_str = program.get("deadline")
                if deadline_str:
                    try:
                        deadline = datetime.fromisoformat(deadline_str.replace('Z', '+00:00'))
                        
                        if datetime.utcnow() <= deadline <= cutoff_date:
                            days_remaining = (deadline - datetime.utcnow()).days
                            
                            deadline_info = {
                                "program": program,
                                "deadline": deadline.isoformat(),
                                "days_remaining": days_remaining
                            }
                            
                            deadlines.append(deadline_info)
                            
                            if days_remaining <= 7:
                                urgent.append(deadline_info)
                    except:
                        continue
            
            # Sort by deadline
            deadlines.sort(key=lambda x: x["days_remaining"])
            urgent.sort(key=lambda x: x["days_remaining"])
            
            return {
                "deadlines": deadlines,
                "urgent": urgent
            }
            
        except Exception as e:
            logger.error(f"Error monitoring deadlines: {str(e)}")
            raise
    
    async def _process_crawled_page(
        self,
        url: str,
        html_content: str,
        markdown_content: str
    ) -> Dict[str, Any]:
        """Process a crawled page and store in database"""
        try:
            # Clean and process content
            clean_text = self.processor.clean_html(html_content)
            metadata = self.processor.extract_metadata(html_content, url)
            
            # Generate embedding
            from .utils import EmbeddingGenerator
            embedding_gen = EmbeddingGenerator()
            embedding = await embedding_gen.generate_embedding(clean_text[:2000])
            
            # Store in database
            result = await self.supabase.store_crawled_page(
                url=url,
                content=clean_text,
                metadata=metadata,
                embedding=embedding
            )
            
            return {
                "url": url,
                "content_length": len(clean_text),
                "metadata": metadata,
                "stored": result.get("success", False)
            }
            
        except Exception as e:
            logger.error(f"Error processing crawled page: {str(e)}")
            return {"url": url, "error": str(e)}
    
    async def _extract_funding_info(
        self,
        content: str,
        source_url: str
    ) -> Dict[str, Any]:
        """Extract funding information using pattern matching"""
        try:
            programs = []
            calls = []
            
            # Simple pattern-based extraction
            for pattern_name, pattern in self.funding_patterns.items():
                matches = re.findall(pattern, content)
                if matches:
                    logger.info(f"Found {len(matches)} matches for {pattern_name}")
            
            # This would be expanded with more sophisticated extraction
            return {
                "programs": programs,
                "calls": calls
            }
            
        except Exception as e:
            logger.error(f"Error extracting funding info: {str(e)}")
            return {"programs": [], "calls": []}
    
    def _extract_funding_urls(
        self,
        html_content: str,
        base_url: str,
        include_patterns: List[str]
    ) -> List[str]:
        """Extract relevant funding URLs from HTML content"""
        try:
            from bs4 import BeautifulSoup
            
            soup = BeautifulSoup(html_content, 'html.parser')
            urls = []
            
            # Find all links
            for link in soup.find_all('a', href=True):
                href = link['href']
                full_url = urljoin(base_url, href)
                
                # Check if URL matches funding patterns
                if any(pattern in full_url.lower() for pattern in ['call', 'funding', 'grant', 'programme']):
                    urls.append(full_url)
                
                # Check include patterns
                if include_patterns:
                    if any(re.search(pattern, full_url) for pattern in include_patterns):
                        urls.append(full_url)
            
            # Remove duplicates and limit
            return list(set(urls))[:20]
            
        except Exception as e:
            logger.error(f"Error extracting funding URLs: {str(e)}")
            return []
