"""
Context Engineering Patterns for EU Funds MCP Server
Implements advanced context-aware processing and multi-step reasoning
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import json

# External imports
import openai

# Local imports
from .utils import SupabaseManager, RAGSystem, EmbeddingGenerator

logger = logging.getLogger(__name__)


class ContextEngine:
    """Advanced context engineering for funding opportunity analysis"""
    
    def __init__(
        self,
        supabase_manager: SupabaseManager,
        rag_system: RAGSystem,
        embedding_generator: EmbeddingGenerator
    ):
        self.supabase = supabase_manager
        self.rag = rag_system
        self.embeddings = embedding_generator
        self.openai_client = openai.AsyncOpenAI()
        
        # Context patterns for different analysis types
        self.context_patterns = {
            "eligibility_analysis": {
                "system_prompt": """You are an expert EU funding consultant. Analyze eligibility for funding programs with deep understanding of EU regulations and requirements.""",
                "context_fields": ["organization_type", "project_scope", "budget_range", "geographic_focus", "sector"],
                "reasoning_steps": ["initial_screening", "detailed_analysis", "recommendation_generation"]
            },
            "opportunity_matching": {
                "system_prompt": """You are a funding opportunity matcher. Find the best matches between projects and available funding programs.""",
                "context_fields": ["project_description", "timeline", "expected_outcomes", "partnership_requirements"],
                "reasoning_steps": ["requirement_extraction", "program_matching", "compatibility_scoring"]
            },
            "deadline_prioritization": {
                "system_prompt": """You are a deadline management expert. Prioritize funding opportunities based on deadlines, preparation time, and success probability.""",
                "context_fields": ["preparation_time", "success_probability", "strategic_importance"],
                "reasoning_steps": ["deadline_analysis", "preparation_assessment", "priority_ranking"]
            }
        }
    
    async def analyze_with_context(
        self,
        analysis_type: str,
        input_data: Dict[str, Any],
        context_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Perform context-aware analysis using multi-step reasoning"""
        try:
            if analysis_type not in self.context_patterns:
                raise ValueError(f"Unknown analysis type: {analysis_type}")
            
            pattern = self.context_patterns[analysis_type]
            
            # Step 1: Gather contextual information
            context = await self._gather_context(
                analysis_type=analysis_type,
                input_data=input_data,
                additional_context=context_data or {}
            )
            
            # Step 2: Perform multi-step reasoning
            reasoning_result = await self._multi_step_reasoning(
                pattern=pattern,
                context=context,
                input_data=input_data
            )
            
            # Step 3: Generate final recommendations
            recommendations = await self._generate_recommendations(
                analysis_type=analysis_type,
                reasoning_result=reasoning_result,
                context=context
            )
            
            return {
                "analysis_type": analysis_type,
                "context": context,
                "reasoning": reasoning_result,
                "recommendations": recommendations,
                "confidence_score": reasoning_result.get("confidence", 0.0),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in context analysis: {str(e)}")
            raise
    
    async def contextual_search(
        self,
        query: str,
        context_type: str,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Perform context-aware search with domain-specific enhancement"""
        try:
            # Enhance query with context
            enhanced_query = await self._enhance_query_with_context(
                query=query,
                context_type=context_type,
                filters=filters or {}
            )
            
            # Perform RAG search
            rag_results = await self.rag.search_funding_opportunities(
                query=enhanced_query,
                max_results=20
            )
            
            # Apply contextual reranking
            reranked_results = await self._contextual_reranking(
                results=rag_results,
                original_query=query,
                context_type=context_type
            )
            
            return reranked_results
            
        except Exception as e:
            logger.error(f"Error in contextual search: {str(e)}")
            return []
    
    async def generate_contextual_insights(
        self,
        data: Dict[str, Any],
        insight_type: str = "funding_landscape"
    ) -> Dict[str, Any]:
        """Generate contextual insights about funding landscape"""
        try:
            # Gather relevant data
            funding_programs = await self.supabase.get_funding_programs()
            
            # Create insight prompt
            prompt = self._create_insight_prompt(
                data=data,
                programs=funding_programs,
                insight_type=insight_type
            )
            
            # Generate insights using OpenAI
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are an expert EU funding analyst providing strategic insights."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )
            
            # Parse and structure insights
            insights_text = response.choices[0].message.content
            structured_insights = await self._structure_insights(insights_text)
            
            return {
                "insight_type": insight_type,
                "insights": structured_insights,
                "data_sources": len(funding_programs),
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating contextual insights: {str(e)}")
            return {"error": str(e)}
    
    async def _gather_context(
        self,
        analysis_type: str,
        input_data: Dict[str, Any],
        additional_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Gather relevant contextual information"""
        try:
            context = {
                "analysis_type": analysis_type,
                "timestamp": datetime.utcnow().isoformat(),
                "input_data": input_data,
                "additional_context": additional_context
            }
            
            # Get relevant funding programs
            funding_programs = await self.supabase.get_funding_programs()
            context["available_programs"] = funding_programs
            
            # Get historical data if relevant
            if "organization_type" in input_data:
                # This would query for similar organizations' success rates
                context["historical_success"] = await self._get_historical_success(
                    input_data["organization_type"]
                )
            
            # Add domain-specific context
            if analysis_type == "eligibility_analysis":
                context["eligibility_criteria"] = await self._get_eligibility_criteria()
            elif analysis_type == "opportunity_matching":
                context["matching_algorithms"] = await self._get_matching_criteria()
            
            return context
            
        except Exception as e:
            logger.error(f"Error gathering context: {str(e)}")
            return {"error": str(e)}
    
    async def _multi_step_reasoning(
        self,
        pattern: Dict[str, Any],
        context: Dict[str, Any],
        input_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Perform multi-step reasoning based on pattern"""
        try:
            reasoning_steps = pattern["reasoning_steps"]
            results = {}
            
            for step in reasoning_steps:
                step_result = await self._execute_reasoning_step(
                    step=step,
                    pattern=pattern,
                    context=context,
                    input_data=input_data,
                    previous_results=results
                )
                results[step] = step_result
            
            # Calculate overall confidence
            confidence_scores = [
                result.get("confidence", 0.5) 
                for result in results.values() 
                if isinstance(result, dict)
            ]
            overall_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.5
            
            return {
                "steps": results,
                "confidence": overall_confidence,
                "reasoning_complete": True
            }
            
        except Exception as e:
            logger.error(f"Error in multi-step reasoning: {str(e)}")
            return {"error": str(e), "confidence": 0.0}
    
    async def _execute_reasoning_step(
        self,
        step: str,
        pattern: Dict[str, Any],
        context: Dict[str, Any],
        input_data: Dict[str, Any],
        previous_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a single reasoning step"""
        try:
            # Create step-specific prompt
            prompt = f"""
            {pattern['system_prompt']}
            
            Current reasoning step: {step}
            Input data: {json.dumps(input_data, indent=2)}
            Context: {json.dumps(context.get('available_programs', [])[:5], indent=2)}
            Previous results: {json.dumps(previous_results, indent=2)}
            
            Perform the {step} step and return structured JSON result with:
            - analysis: Your analysis for this step
            - findings: Key findings
            - confidence: Confidence score (0.0-1.0)
            - next_step_input: Data for next step
            """
            
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.2
            )
            
            # Parse response
            try:
                result = json.loads(response.choices[0].message.content)
                return result
            except json.JSONDecodeError:
                return {
                    "analysis": response.choices[0].message.content,
                    "confidence": 0.5,
                    "error": "Failed to parse JSON response"
                }
            
        except Exception as e:
            logger.error(f"Error executing reasoning step {step}: {str(e)}")
            return {"error": str(e), "confidence": 0.0}
    
    async def _generate_recommendations(
        self,
        analysis_type: str,
        reasoning_result: Dict[str, Any],
        context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate final recommendations based on reasoning"""
        try:
            prompt = f"""
            Based on the analysis type '{analysis_type}' and reasoning results, generate specific, actionable recommendations.
            
            Reasoning results: {json.dumps(reasoning_result, indent=2)}
            Context: {json.dumps(context.get('available_programs', [])[:3], indent=2)}
            
            Return JSON array of recommendations with:
            - title: Recommendation title
            - description: Detailed description
            - priority: Priority level (high/medium/low)
            - action_items: List of specific actions
            - timeline: Suggested timeline
            - success_probability: Estimated success probability
            """
            
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3
            )
            
            try:
                recommendations = json.loads(response.choices[0].message.content)
                if not isinstance(recommendations, list):
                    recommendations = [recommendations]
                return recommendations
            except json.JSONDecodeError:
                return [{
                    "title": "Analysis Complete",
                    "description": response.choices[0].message.content,
                    "priority": "medium",
                    "action_items": ["Review analysis results"],
                    "timeline": "Immediate",
                    "success_probability": 0.5
                }]
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return [{"error": str(e)}]
    
    async def _enhance_query_with_context(
        self,
        query: str,
        context_type: str,
        filters: Dict[str, Any]
    ) -> str:
        """Enhance search query with contextual information"""
        try:
            context_enhancements = {
                "funding_search": "EU funding opportunities grants programs",
                "eligibility_check": "eligibility criteria requirements conditions",
                "deadline_search": "deadline application submission closing date",
                "program_analysis": "program analysis evaluation assessment"
            }
            
            enhancement = context_enhancements.get(context_type, "")
            enhanced_query = f"{query} {enhancement}"
            
            # Add filter-based enhancements
            if "organization_type" in filters:
                enhanced_query += f" {filters['organization_type']}"
            if "sector" in filters:
                enhanced_query += f" {filters['sector']}"
            
            return enhanced_query.strip()
            
        except Exception as e:
            logger.error(f"Error enhancing query: {str(e)}")
            return query
    
    async def _contextual_reranking(
        self,
        results: List[Dict[str, Any]],
        original_query: str,
        context_type: str
    ) -> List[Dict[str, Any]]:
        """Rerank results based on contextual relevance"""
        try:
            # Simple reranking based on context type
            context_weights = {
                "funding_search": {"title": 0.3, "content": 0.4, "similarity_score": 0.3},
                "eligibility_check": {"content": 0.5, "similarity_score": 0.3, "metadata": 0.2},
                "deadline_search": {"metadata": 0.4, "content": 0.3, "similarity_score": 0.3}
            }
            
            weights = context_weights.get(context_type, {"similarity_score": 1.0})
            
            # Calculate contextual scores
            for result in results:
                contextual_score = 0.0
                
                for field, weight in weights.items():
                    if field == "similarity_score":
                        contextual_score += result.get("similarity_score", 0.0) * weight
                    elif field in result:
                        # Simple text relevance scoring
                        field_relevance = len([
                            word for word in original_query.lower().split()
                            if word in str(result[field]).lower()
                        ]) / len(original_query.split())
                        contextual_score += field_relevance * weight
                
                result["contextual_score"] = contextual_score
            
            # Sort by contextual score
            results.sort(key=lambda x: x.get("contextual_score", 0.0), reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in contextual reranking: {str(e)}")
            return results
    
    def _create_insight_prompt(
        self,
        data: Dict[str, Any],
        programs: List[Dict[str, Any]],
        insight_type: str
    ) -> str:
        """Create prompt for insight generation"""
        return f"""
        Generate strategic insights about the EU funding landscape.
        
        Insight type: {insight_type}
        Input data: {json.dumps(data, indent=2)}
        Available programs: {len(programs)} programs
        Sample programs: {json.dumps(programs[:3], indent=2)}
        
        Provide insights on:
        - Current funding trends
        - Opportunity gaps
        - Strategic recommendations
        - Market analysis
        - Success factors
        """
    
    async def _structure_insights(self, insights_text: str) -> Dict[str, Any]:
        """Structure raw insights into organized format"""
        try:
            # Simple structuring - could be enhanced with NLP
            return {
                "summary": insights_text[:500] + "..." if len(insights_text) > 500 else insights_text,
                "key_points": insights_text.split('\n')[:10],
                "full_analysis": insights_text
            }
        except Exception as e:
            logger.error(f"Error structuring insights: {str(e)}")
            return {"error": str(e)}
    
    async def _get_historical_success(self, organization_type: str) -> Dict[str, Any]:
        """Get historical success data for organization type"""
        # Placeholder - would query historical data
        return {"success_rate": 0.65, "average_funding": 250000}
    
    async def _get_eligibility_criteria(self) -> Dict[str, Any]:
        """Get common eligibility criteria"""
        return {
            "common_criteria": ["EU entity", "legal entity", "financial capacity"],
            "exclusions": ["profit maximization", "illegal activities"]
        }
    
    async def _get_matching_criteria(self) -> Dict[str, Any]:
        """Get matching criteria for opportunity matching"""
        return {
            "primary_factors": ["scope_alignment", "budget_match", "timeline_compatibility"],
            "secondary_factors": ["geographic_focus", "partnership_requirements"]
        }
