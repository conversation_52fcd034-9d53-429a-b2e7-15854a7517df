# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=text-embedding-3-small

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# Crawling Configuration
CRAWL_DELAY=1.0
MAX_CONCURRENT_REQUESTS=5
RESPECT_ROBOTS_TXT=true
USER_AGENT=EU-Funds-MCP-Server/1.0

# RAG Configuration
EMBEDDING_DIMENSION=1536
CHUNK_SIZE=800
CHUNK_OVERLAP=200
MAX_SEARCH_RESULTS=10

# MCP Server Configuration
MCP_SERVER_NAME=eu-funds-mcp-server
MCP_SERVER_VERSION=1.0.0
LOG_LEVEL=INFO

# Performance Configuration
DATABASE_POOL_SIZE=10
REQUEST_TIMEOUT=30
EMBEDDING_BATCH_SIZE=100

