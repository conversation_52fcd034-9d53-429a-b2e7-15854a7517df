# 🧪 Testing Strategy - EU Funds MCP Server

## 🎯 Testing Philosophy
- **Quality First** - No code without tests
- **Async Testing** - All tests use pytest-asyncio
- **Mock External** - Mock OpenAI, Supabase, web requests
- **Performance** - Benchmark critical operations

## 📋 Test Categories

### Unit Tests (`tests/unit/`)
- **utils.py** - Core utilities testing
- **eu_funds_processor.py** - Content extraction logic
- **context_engine.py** - Context engineering
- **rag_system.py** - RAG functionality

### Integration Tests (`tests/integration/`)
- **MCP tools** - End-to-end tool testing
- **Database** - Supabase integration
- **Crawling** - Web crawling workflows
- **RAG pipeline** - Full RAG workflow

### Performance Tests (`tests/performance/`)
- **Crawling speed** - Pages per minute
- **RAG latency** - Query response times
- **Memory usage** - Resource consumption
- **Concurrent load** - Multiple simultaneous requests

## 🔧 Test Configuration
```python
# pytest.ini equivalent
[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
```

## 📊 Coverage Requirements
- **Minimum coverage**: 80%
- **Critical paths**: 95%
- **MCP tools**: 100%
- **Error handling**: 90%


