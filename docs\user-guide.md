# 📚 User Guide - EU Funds MCP Server

## 🚀 Getting Started

### Installation
1. Install Python 3.11+
2. Clone repository
3. Install dependencies: `pip install -r requirements.txt`
4. Configure environment: Copy `config/.env.example` to `config/.env`
5. Add API keys (OpenAI, Supabase)
6. Run: `python start_server.py`

### Claude Desktop Integration
Add to Claude <PERSON> config:
```json
{
  "mcpServers": {
    "eu-funds": {
      "command": "python",
      "args": ["start_server.py"],
      "cwd": "/path/to/eu-funds-mcp-server"
    }
  }
}
```

## 🛠️ Available Tools

### crawl_eu_funds_site
Crawl and index EU funding websites.
```
Parameters:
- url: Website URL to crawl
- max_pages: Maximum pages to crawl (default: 50)
- depth: Crawling depth (default: 2)
```

### search_funding_opportunities
Intelligent search across all indexed content.
```
Parameters:
- query: Search query in Bulgarian or English
- limit: Maximum results (default: 10)
- filters: Optional filters (program_type, deadline, etc.)
```

## 💡 Best Practices
- Use specific queries for better results
- Check deadlines regularly with monitor_deadlines
- Verify eligibility before applying
- Keep crawled data updated
