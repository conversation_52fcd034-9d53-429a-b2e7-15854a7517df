# ✅ Validation Checklist - EU Funds MCP Server

## 🎯 Master Validation Rules

### 📋 Pre-Task Validation
Before starting ANY task, verify:
- [ ] Previous task marked as ✅ COMPLETED in project-status.md
- [ ] All dependencies satisfied
- [ ] Environment properly configured
- [ ] No blocking issues present

### 📋 Post-Task Validation  
After completing ANY task, verify:
- [ ] All acceptance criteria met
- [ ] Code follows project rules
- [ ] Tests pass (if applicable)
- [ ] Documentation updated
- [ ] project-status.md updated

## 🏗️ Architecture Validation

### File Structure Rules
- [ ] Max 500 lines per file
- [ ] Modular design with clear separation
- [ ] Async/await for all I/O operations
- [ ] Proper import structure (relative for project, absolute for external)

### Code Quality Rules
- [ ] Full type hints on all functions
- [ ] Comprehensive docstrings with Args/Returns
- [ ] Structured error handling with try/catch
- [ ] Logging for all operations
- [ ] No hardcoded values (use environment variables)

### MCP Compliance Rules
- [ ] @mcp.tool() decorator used correctly
- [ ] Dict[str, Any] return type
- [ ] success: bool field in all responses
- [ ] Proper error response structure
- [ ] JSON serializable outputs

## 🕷️ Crawling Validation

### Ethical Crawling Rules
- [ ] robots.txt compliance implemented
- [ ] Rate limiting with configurable delays
- [ ] Graceful error handling for HTTP errors
- [ ] Content validation and deduplication
- [ ] Respectful crawling patterns

### Content Processing Rules
- [ ] HTML cleaning and text extraction
- [ ] Semantic chunking (200-800 tokens)
- [ ] Metadata preservation
- [ ] Language detection
- [ ] Format preservation for structured content

## 🧠 RAG System Validation

### Embedding Rules
- [ ] Consistent embedding model usage
- [ ] Batch processing for efficiency
- [ ] Contextual enrichment of chunks
- [ ] Proper vector dimensions
- [ ] Error handling for API failures

### Search Quality Rules
- [ ] Hybrid search (vector + keyword)
- [ ] Reranking implementation
- [ ] Source attribution
- [ ] Confidence scoring
- [ ] Relevance threshold enforcement

### Context Engineering Rules
- [ ] Multi-step reasoning capability
- [ ] Context-aware chunk selection
- [ ] Domain-specific knowledge integration
- [ ] Hallucination prevention measures
- [ ] Source verification

## 🗄️ Database Validation

### Supabase Integration Rules
- [ ] Connection pooling implemented
- [ ] Parameterized queries only
- [ ] Batch operations for efficiency
- [ ] Transaction handling
- [ ] Error recovery mechanisms

### Vector Storage Rules
- [ ] Proper pgvector usage
- [ ] Efficient indexing strategy
- [ ] Consistent vector dimensions
- [ ] Metadata storage optimization
- [ ] Query performance optimization

## 🧪 Testing Validation

### Unit Testing Rules
- [ ] All utility functions tested
- [ ] Mock external dependencies
- [ ] Edge case coverage
- [ ] Error scenario testing
- [ ] Performance assertions

### Integration Testing Rules
- [ ] End-to-end MCP tool testing
- [ ] Database integration testing
- [ ] API integration testing
- [ ] Error propagation testing
- [ ] Timeout handling testing

## 🔒 Security Validation

### Environment Security Rules
- [ ] No hardcoded API keys
- [ ] Proper environment variable usage
- [ ] Input validation on all user inputs
- [ ] SQL injection prevention
- [ ] Rate limiting implementation

### Data Security Rules
- [ ] Sensitive data handling
- [ ] Proper error message sanitization
- [ ] Access control implementation
- [ ] Audit logging
- [ ] GDPR compliance considerations

## 🌐 Bulgarian Communication Validation

### Language Rules
- [ ] Bulgarian responses for Bulgarian input
- [ ] English responses for English input
- [ ] English code elements (variables, functions, comments)
- [ ] Consistent technical terminology

### Communication Style Rules
- [ ] Direct and clear responses
- [ ] Technical precision
- [ ] Practical examples provided
- [ ] Professional tone maintained

## 📊 Performance Validation

### Response Time Rules
- [ ] RAG queries < 2 seconds
- [ ] Crawling > 10 pages/minute
- [ ] Embedding generation < 500ms per chunk
- [ ] Database queries < 100ms average

### Resource Usage Rules
- [ ] Memory usage < 1GB normal operation
- [ ] CPU usage reasonable under load
- [ ] Database connection efficiency
- [ ] API rate limit compliance

## 🚨 Failure Handling

### When Validation Fails
1. **Stop immediately** - Do not proceed to next task
2. **Document the failure** in project-status.md
3. **Identify root cause** and required fixes
4. **Create remediation plan** with specific steps
5. **Re-validate after fixes** before proceeding

### Rollback Procedures
- [ ] Revert code changes if necessary
- [ ] Update project-status.md with rollback
- [ ] Document lessons learned
- [ ] Adjust validation criteria if needed

---

## 🎯 Validation Workflow

### Before Starting Any Task:
1. Check project-status.md for current state
2. Verify all dependencies completed
3. Review relevant validation rules
4. Confirm environment setup

### During Task Execution:
1. Follow all applicable validation rules
2. Test incrementally as you build
3. Document any issues encountered
4. Maintain code quality standards

### After Task Completion:
1. Run through complete validation checklist
2. Test all functionality thoroughly
3. Update all relevant documentation
4. Mark task as completed in project-status.md
5. Identify and document next steps

---

*This checklist must be followed for EVERY task*  
*No exceptions - quality is non-negotiable*