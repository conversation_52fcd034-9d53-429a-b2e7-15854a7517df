# EU Funds MCP Server Environment Configuration
# Copy this file to .env and fill in your actual values

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
MODEL_CHOICE=gpt-4o-mini

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Crawling Configuration
CRAWL_DELAY=1.0
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=30
RESPECT_ROBOTS_TXT=true
USER_AGENT=EU-Funds-MCP-Server/1.0

# RAG Configuration
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_DIMENSION=1536
CHUNK_SIZE=800
CHUNK_OVERLAP=200
MAX_SEARCH_RESULTS=10
SIMILARITY_THRESHOLD=0.7

# Server Configuration
MCP_SERVER_NAME=eu-funds-mcp
MCP_SERVER_VERSION=1.0.0
LOG_LEVEL=INFO
DEBUG_MODE=false

# Database Configuration
DB_POOL_SIZE=10
DB_TIMEOUT=30
BATCH_SIZE=100

# Performance Configuration
MAX_MEMORY_MB=1024
CACHE_TTL_SECONDS=3600

