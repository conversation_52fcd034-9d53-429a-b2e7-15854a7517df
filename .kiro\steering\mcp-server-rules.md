# MCP Server Development Rules

## 🎯 Основни принципи за MCP сървър разработка

### Архитектурни стандарти
- **Моду<PERSON>на структура** - всеки компонент в отделен файл
- **Async/await** - всички I/O операции да бъдат асинхронни
- **Error handling** - robust обработка на всички грешки
- **Logging** - подробно логиране на всички операции

### MCP Protocol съответствие
- **Стандартни tool signatures** - следвай MCP спецификацията точно
- **JSON serializable** - всички return values да са JSON съвместими
- **Type hints** - използвай пълни type annotations
- **Docstrings** - всеки tool да има подробна документация

### Crawling качество
- **Respect robots.txt** - винаги проверявай и спазвай robots.txt
- **Rate limiting** - не претоварвай сайтовете
- **Content validation** - проверявай качеството на извлеченото съдържание
- **Deduplication** - избягвай дублирано съдържание

### RAG оптимизация
- **Chunk quality** - интелигентно разделяне на съдържанието
- **Embedding efficiency** - batch процесиране на embeddings
- **Search relevance** - използвай reranking за по-добри резултати
- **Source attribution** - винаги предоставяй източници

### Supabase интеграция
- **Connection pooling** - ефективно управление на връзките
- **Batch operations** - групирай операциите за по-добра производителност
- **Vector indexing** - оптимизирай vector search
- **Data consistency** - осигури консистентност на данните

### Performance стандарти
- **Response time** < 2 секунди за RAG заявки
- **Crawling speed** > 10 страници в минута
- **Memory usage** < 1GB за нормална работа
- **Error rate** < 5% от всички операции

### Security правила
- **Environment variables** - никога не hardcode API keys
- **Input validation** - валидирай всички входни данни
- **SQL injection protection** - използвай parameterized queries
- **Rate limiting** - защити срещу abuse

### Testing изисквания
- **Unit tests** за всеки tool
- **Integration tests** за цялостни workflow-ове
- **Performance tests** за критични операции
- **Error scenario tests** за edge cases