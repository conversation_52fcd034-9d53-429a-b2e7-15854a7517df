# 🗄️ Database Schema - EU Funds MCP Server

## 📊 Schema Overview

### Design Principles
- **Normalized structure** - Minimize data duplication
- **Vector optimization** - Efficient pgvector usage
- **Relationship integrity** - Proper foreign key constraints
- **Performance indexing** - Strategic index placement
- **Scalability** - Support for large datasets

## 📋 Table Definitions

### 1. crawled_pages
Primary table for all crawled content with embeddings.

```sql
CREATE TABLE crawled_pages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL UNIQUE,
    title TEXT,
    content TEXT NOT NULL,
    content_type VARCHAR(50) DEFAULT 'general',
    language VARCHAR(10) DEFAULT 'bg',
    embedding VECTOR(1536), -- OpenAI text-embedding-3-small
    metadata JSONB DEFAULT '{}',
    crawled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    content_hash TEXT, -- For deduplication
    source_domain TEXT,
    
    -- Indexes
    CONSTRAINT crawled_pages_url_key UNIQUE (url)
);

-- Vector similarity index
CREATE INDEX crawled_pages_embedding_idx ON crawled_pages 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Performance indexes
CREATE INDEX crawled_pages_content_type_idx ON crawled_pages (content_type);
CREATE INDEX crawled_pages_language_idx ON crawled_pages (language);
CREATE INDEX crawled_pages_crawled_at_idx ON crawled_pages (crawled_at DESC);
CREATE INDEX crawled_pages_source_domain_idx ON crawled_pages (source_domain);
CREATE INDEX crawled_pages_content_hash_idx ON crawled_pages (content_hash);

-- Full-text search
CREATE INDEX crawled_pages_content_fts_idx ON crawled_pages 
USING gin(to_tsvector('bulgarian', content));
```

### 2. funding_programs
Structured data for EU funding programs.

```sql
CREATE TABLE funding_programs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    program_code VARCHAR(50) UNIQUE,
    title TEXT NOT NULL,
    description TEXT,
    total_budget DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'EUR',
    start_date DATE,
    end_date DATE,
    managing_authority TEXT,
    program_type VARCHAR(50), -- 'operational', 'direct', 'shared'
    priority_axes JSONB DEFAULT '[]',
    eligible_beneficiaries JSONB DEFAULT '[]',
    source_url TEXT,
    source_page_id UUID REFERENCES crawled_pages(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Indexes
CREATE INDEX funding_programs_program_code_idx ON funding_programs (program_code);
CREATE INDEX funding_programs_program_type_idx ON funding_programs (program_type);
CREATE INDEX funding_programs_dates_idx ON funding_programs (start_date, end_date);
CREATE INDEX funding_programs_active_idx ON funding_programs (is_active);
```

### 3. calls_for_proposals
Active and historical calls for proposals.

```sql
CREATE TABLE calls_for_proposals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    call_code VARCHAR(100) UNIQUE,
    title TEXT NOT NULL,
    description TEXT,
    program_id UUID REFERENCES funding_programs(id),
    budget_available DECIMAL(15,2),
    min_grant_amount DECIMAL(15,2),
    max_grant_amount DECIMAL(15,2),
    co_financing_rate DECIMAL(5,2), -- Percentage
    application_deadline TIMESTAMP WITH TIME ZONE,
    results_announcement DATE,
    project_start_date DATE,
    project_end_date DATE,
    eligible_activities JSONB DEFAULT '[]',
    eligibility_criteria JSONB DEFAULT '{}',
    required_documents JSONB DEFAULT '[]',
    contact_info JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'open', 'closed', 'evaluated'
    source_url TEXT,
    source_page_id UUID REFERENCES crawled_pages(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Indexes
CREATE INDEX calls_for_proposals_call_code_idx ON calls_for_proposals (call_code);
CREATE INDEX calls_for_proposals_program_id_idx ON calls_for_proposals (program_id);
CREATE INDEX calls_for_proposals_status_idx ON calls_for_proposals (status);
CREATE INDEX calls_for_proposals_deadline_idx ON calls_for_proposals (application_deadline);
CREATE INDEX calls_for_proposals_active_idx ON calls_for_proposals (is_active);
```

### 4. content_chunks
Semantic chunks for improved RAG performance.

```sql
CREATE TABLE content_chunks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    page_id UUID REFERENCES crawled_pages(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    embedding VECTOR(1536),
    token_count INTEGER,
    chunk_type VARCHAR(50) DEFAULT 'paragraph', -- 'paragraph', 'table', 'list', 'code'
    context_before TEXT, -- Previous chunk for context
    context_after TEXT,  -- Next chunk for context
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique chunk ordering per page
    CONSTRAINT content_chunks_page_chunk_unique UNIQUE (page_id, chunk_index)
);

-- Vector similarity index
CREATE INDEX content_chunks_embedding_idx ON content_chunks 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Performance indexes
CREATE INDEX content_chunks_page_id_idx ON content_chunks (page_id);
CREATE INDEX content_chunks_chunk_type_idx ON content_chunks (chunk_type);
CREATE INDEX content_chunks_token_count_idx ON content_chunks (token_count);
```

### 5. documents
Structured document metadata and processing status.

```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename TEXT NOT NULL,
    original_url TEXT,
    file_type VARCHAR(20), -- 'pdf', 'doc', 'docx', 'xls', 'xlsx'
    file_size BIGINT,
    content_extracted TEXT,
    processing_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    processing_error TEXT,
    page_count INTEGER,
    language VARCHAR(10) DEFAULT 'bg',
    related_program_id UUID REFERENCES funding_programs(id),
    related_call_id UUID REFERENCES calls_for_proposals(id),
    source_page_id UUID REFERENCES crawled_pages(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Indexes
CREATE INDEX documents_file_type_idx ON documents (file_type);
CREATE INDEX documents_processing_status_idx ON documents (processing_status);
CREATE INDEX documents_related_program_idx ON documents (related_program_id);
CREATE INDEX documents_related_call_idx ON documents (related_call_id);
```

### 6. search_queries
Query logging and analytics.

```sql
CREATE TABLE search_queries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_text TEXT NOT NULL,
    query_type VARCHAR(50), -- 'rag', 'program_search', 'call_search'
    results_count INTEGER,
    response_time_ms INTEGER,
    user_session VARCHAR(100),
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics indexes
CREATE INDEX search_queries_query_type_idx ON search_queries (query_type);
CREATE INDEX search_queries_created_at_idx ON search_queries (created_at DESC);
CREATE INDEX search_queries_success_idx ON search_queries (success);
```

### 7. crawl_sessions
Crawling session tracking and statistics.

```sql
CREATE TABLE crawl_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_name VARCHAR(100),
    start_url TEXT NOT NULL,
    pages_crawled INTEGER DEFAULT 0,
    pages_failed INTEGER DEFAULT 0,
    total_content_size BIGINT DEFAULT 0,
    crawl_depth INTEGER DEFAULT 1,
    respect_robots_txt BOOLEAN DEFAULT true,
    crawl_delay DECIMAL(4,2) DEFAULT 1.0,
    status VARCHAR(20) DEFAULT 'running', -- 'running', 'completed', 'failed', 'paused'
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}'
);

-- Performance indexes
CREATE INDEX crawl_sessions_status_idx ON crawl_sessions (status);
CREATE INDEX crawl_sessions_started_at_idx ON crawl_sessions (started_at DESC);
```

## 🔗 Relationships & Constraints

### Foreign Key Relationships
```sql
-- Programs can have multiple calls
calls_for_proposals.program_id → funding_programs.id

-- Pages can have multiple chunks
content_chunks.page_id → crawled_pages.id

-- Documents can be related to programs and calls
documents.related_program_id → funding_programs.id
documents.related_call_id → calls_for_proposals.id

-- Content can reference source pages
funding_programs.source_page_id → crawled_pages.id
calls_for_proposals.source_page_id → crawled_pages.id
documents.source_page_id → crawled_pages.id
```

### Data Integrity Constraints
```sql
-- Ensure valid dates
ALTER TABLE funding_programs ADD CONSTRAINT funding_programs_dates_check 
CHECK (start_date <= end_date);

ALTER TABLE calls_for_proposals ADD CONSTRAINT calls_dates_check 
CHECK (project_start_date <= project_end_date);

-- Ensure valid budget amounts
ALTER TABLE calls_for_proposals ADD CONSTRAINT calls_budget_check 
CHECK (min_grant_amount <= max_grant_amount);

-- Ensure valid co-financing rate
ALTER TABLE calls_for_proposals ADD CONSTRAINT calls_cofinancing_check 
CHECK (co_financing_rate >= 0 AND co_financing_rate <= 100);
```

## 📊 Views for Common Queries

### Active Funding Opportunities
```sql
CREATE VIEW active_funding_opportunities AS
SELECT 
    c.id,
    c.call_code,
    c.title,
    c.application_deadline,
    c.budget_available,
    c.min_grant_amount,
    c.max_grant_amount,
    p.title as program_title,
    p.program_code
FROM calls_for_proposals c
JOIN funding_programs p ON c.program_id = p.id
WHERE c.status = 'open' 
  AND c.application_deadline > NOW()
  AND c.is_active = true
  AND p.is_active = true
ORDER BY c.application_deadline ASC;
```

### Content Search View
```sql
CREATE VIEW searchable_content AS
SELECT 
    cp.id,
    cp.url,
    cp.title,
    cp.content,
    cp.content_type,
    cp.language,
    cp.embedding,
    cp.crawled_at,
    COALESCE(fp.title, cfp.title) as related_title,
    COALESCE(fp.program_code, cfp.call_code) as related_code
FROM crawled_pages cp
LEFT JOIN funding_programs fp ON cp.id = fp.source_page_id
LEFT JOIN calls_for_proposals cfp ON cp.id = cfp.source_page_id
WHERE cp.is_active = true;
```

## 🚀 Performance Optimization

### Vector Search Optimization
```sql
-- Optimize vector search performance
SET ivfflat.probes = 10;

-- Create specialized indexes for common queries
CREATE INDEX crawled_pages_type_embedding_idx ON crawled_pages (content_type, embedding)
WHERE content_type IN ('program', 'call', 'document');
```

### Query Performance
```sql
-- Composite indexes for common filter combinations
CREATE INDEX calls_status_deadline_idx ON calls_for_proposals (status, application_deadline)
WHERE status = 'open';

CREATE INDEX programs_active_type_idx ON funding_programs (is_active, program_type)
WHERE is_active = true;
```

## 🔄 Migration Strategy

### Version Control
- **Schema versioning** - Track all schema changes
- **Migration scripts** - Automated database updates
- **Rollback procedures** - Safe rollback mechanisms
- **Data validation** - Ensure data integrity after migrations

### Backup Strategy
- **Regular backups** - Automated daily backups
- **Point-in-time recovery** - Transaction log backups
- **Cross-region replication** - Disaster recovery
- **Testing procedures** - Regular backup testing

---

*This schema supports efficient vector search, structured data queries, and scalable growth*  
*All tables optimized for the EU Funds MCP server use cases*